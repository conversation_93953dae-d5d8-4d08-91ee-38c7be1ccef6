#!/usr/bin/env python3
"""
Test transformer model with the sample resume PDF
"""

import os
import sys
import json

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from file_processors.pdf_processor import PDFProcessor
from llm_providers.transformer_llm import Transformer<PERSON>MProvider

def main():
    print("🔍 TESTING TRANSFORMER MODEL WITH SAMPLE RESUME")
    print("="*60)
    
    # Use the sample resume PDF
    pdf_path = "docs/sample_resume.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ Sample resume not found: {pdf_path}")
        return
    
    print(f"📄 Processing: {pdf_path}")
    
    # Extract text
    processor = PDFProcessor()
    try:
        text = processor.extract_text(pdf_path)
        print(f"✅ Extracted {len(text)} characters")
        
        # Show first part of extracted text
        print(f"\n📝 Extracted text preview:")
        print("-" * 50)
        print(text[:800])
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ Text extraction failed: {e}")
        return
    
    # Test transformer model
    print(f"\n🤖 Testing transformer model...")
    transformer = TransformerLLMProvider()
    
    if not transformer.is_available():
        print("❌ Transformer models not available")
        return
    
    print("✅ Transformer models loaded")
    
    # Extract resume data
    try:
        resume_data = transformer.extract_resume_data(text)
        
        print(f"\n📋 EXTRACTION RESULTS:")
        print("="*50)
        
        output_dict = resume_data.to_dict()
        
        # Show key extracted information
        print(f"👤 Name: {output_dict.get('first_name', '')} {output_dict.get('last_name', '')}")
        print(f"📧 Email: {output_dict.get('email', 'Not found')}")
        print(f"📱 Phone: {output_dict.get('phone', 'Not found')}")
        
        address = output_dict.get('address', {})
        print(f"🏠 Address: {address.get('city', '')}, {address.get('state', '')}, {address.get('country', '')}")
        
        skills = output_dict.get('skills', [])
        print(f"🛠️  Skills ({len(skills)} found):")
        for skill in skills[:10]:  # Show first 10
            print(f"   • {skill.get('skill', '')}")
        if len(skills) > 10:
            print(f"   ... and {len(skills) - 10} more")
        
        education = output_dict.get('education_history', [])
        print(f"🎓 Education ({len(education)} entries):")
        for edu in education:
            print(f"   • {edu.get('degree', '')} at {edu.get('name', '')} ({edu.get('from_date', '')} - {edu.get('to_date', '')})")
        
        work_history = output_dict.get('work_history', [])
        print(f"💼 Work Experience ({len(work_history)} entries):")
        for work in work_history:
            print(f"   • {work.get('title', '')} at {work.get('company', '')} ({work.get('from_date', '')} - {work.get('to_date', '')})")
            if work.get('description'):
                desc = work.get('description', '')[:100] + "..." if len(work.get('description', '')) > 100 else work.get('description', '')
                print(f"     Description: {desc}")
        
        # Save full output
        output_file = "sample_resume_transformer_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_dict, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Full output saved to: {output_file}")
        
        # Analysis
        print(f"\n🔍 ANALYSIS:")
        print("="*50)
        
        issues = []
        
        if not output_dict.get('first_name') and not output_dict.get('last_name'):
            issues.append("❌ Name extraction failed")
        else:
            print("✅ Name extraction successful")
        
        if not output_dict.get('email'):
            issues.append("❌ Email extraction failed")
        else:
            print("✅ Email extraction successful")
        
        if not output_dict.get('phone'):
            issues.append("❌ Phone extraction failed")
        else:
            print("✅ Phone extraction successful")
        
        if len(skills) < 5:
            issues.append(f"⚠️  Only {len(skills)} skills found (expected more)")
        else:
            print(f"✅ Skills extraction good ({len(skills)} found)")
        
        if len(education) == 0:
            issues.append("❌ No education found")
        else:
            print(f"✅ Education extraction successful ({len(education)} entries)")
        
        if len(work_history) == 0:
            issues.append("❌ No work experience found")
        else:
            print(f"✅ Work experience extraction successful ({len(work_history)} entries)")
        
        if issues:
            print(f"\n🚨 ISSUES FOUND:")
            for issue in issues:
                print(f"   {issue}")
        else:
            print(f"\n🎉 All extractions successful!")
        
    except Exception as e:
        print(f"❌ Resume extraction failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
