<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Loading Portfolio...</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background: linear-gradient(to bottom, #1a202c, #000000, #1a202c);
      color: white;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .container {
      text-align: center;
      max-width: 600px;
      padding: 2rem;
    }
    h1 {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
    }
    .highlight {
      color: #3b82f6;
    }
    .spinner {
      width: 80px;
      height: 80px;
      margin: 2rem auto;
      border-radius: 50%;
      border: 6px solid rgba(59, 130, 246, 0.2);
      border-top-color: #3b82f6;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
    p {
      font-size: 1.25rem;
      color: #d1d5db;
      margin-bottom: 1rem;
    }
    .small {
      font-size: 0.875rem;
      color: #9ca3af;
    }
    .refresh-btn {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      font-size: 1rem;
      cursor: pointer;
      margin-top: 2rem;
      transition: background-color 0.2s;
    }
    .refresh-btn:hover {
      background-color: #2563eb;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1><span class="highlight">Anil Sahith</span>'s Portfolio</h1>
    <div class="spinner"></div>
    <p>Loading terminal environment...</p>
    <p class="small">If the page doesn't load automatically, please click the button below.</p>
    <button class="refresh-btn" onclick="window.location.reload()">Refresh Page</button>
  </div>
  <script>
    // Auto-refresh after a delay
    setTimeout(() => {
      window.location.reload();
    }, 3000);
  </script>
</body>
</html>
