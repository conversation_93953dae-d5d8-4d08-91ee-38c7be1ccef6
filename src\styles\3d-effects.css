/* 3D Effects and Advanced Animations */

/* Base 3D perspective container */
.perspective-container {
  perspective: 1500px;
  transform-style: preserve-3d;
}

/* 3D Card Effects */
.card-3d {
  transform-style: preserve-3d;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Faster transition */
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Light mode hover - simplified to just scale up */
:root:not(.dark) .card-3d:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow:
    0 15px 20px -5px rgba(0, 0, 0, 0.15),
    0 8px 8px -5px rgba(0, 0, 0, 0.08);
}

/* Dark mode hover - simplified to just scale up */
.dark .card-3d:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow:
    0 15px 20px -5px rgba(0, 0, 0, 0.3),
    0 8px 8px -5px rgba(0, 0, 0, 0.15);
}

/* Dynamic gradient shadow */
.gradient-shadow {
  position: relative;
}

.gradient-shadow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Faster transition */
  background: radial-gradient(
    circle at center,
    rgba(103, 232, 249, 0.3) 0%,
    rgba(103, 232, 249, 0) 70%
  );
  transform: translateZ(-1px) scale(0.95);
  filter: blur(20px);
}

.gradient-shadow:hover::after {
  opacity: 1;
  transform: translateZ(-1px) scale(1.05);
}

/* Mouse tracking 3D effect */
.mouse-tracking-3d {
  transition: transform 0.2s ease;
  transform-style: preserve-3d;
}

/* Expanding text effect */
.expanding-text {
  transition: all 0.25s ease; /* Faster transition */
  display: inline-block;
}

.expanding-text:hover {
  transform: scale(1.05);
  letter-spacing: 0.02em;
  /* Remove background override - let component handle gradient */
}

/* Advanced text effects */
.text-glow {
  text-shadow: 0 0 10px currentColor;
  transition: text-shadow 0.3s ease;
}

.text-glow:hover {
  text-shadow: 0 0 15px currentColor;
}

/* Animated underline effect */
.animated-underline {
  position: relative;
  display: inline-block;
}

.animated-underline::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: currentColor;
  transition: all 0.3s ease-out;
  transform: translateX(-50%);
}

.animated-underline:hover::after {
  width: 100%;
}

/* Staggered letter animation */
.staggered-text span {
  display: inline-block;
  transition: transform 0.2s ease;
  /* Remove color transition - let components handle gradient */
}

.staggered-text:hover span {
  /* Remove color override - let components handle gradient */
  transform: translateY(-2px);
}

/* Text reveal effect */
.text-reveal {
  position: relative;
  display: inline-block;
  color: transparent;
  overflow: hidden;
}

.text-reveal::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  color: currentColor;
  overflow: hidden;
  white-space: nowrap;
  animation: revealText 2s ease forwards;
}

@keyframes revealText {
  0% { width: 0; }
  100% { width: 100%; }
}

/* Gradient border effect */
.gradient-border {
  position: relative;
  background-clip: padding-box;
  border: 1px solid transparent;
}

.gradient-border::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  margin: -1px;
  border-radius: inherit;
  background: linear-gradient(
    to right,
    #3b82f6,
    #8b5cf6,
    #ec4899,
    #3b82f6
  );
  background-size: 300% 100%;
  animation: gradientBorder 8s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gradient-border:hover::before {
  opacity: 1;
}

@keyframes gradientBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 3D Button Effects */
.button-3d {
  position: relative;
  transform-style: preserve-3d;
  transition: all 0.2s ease;
  transform: translateZ(0);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

.button-3d:hover {
  transform: translateY(-2px) translateZ(10px);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.button-3d:active {
  transform: translateY(1px) translateZ(0);
  box-shadow:
    0 2px 4px -1px rgba(0, 0, 0, 0.1),
    0 1px 2px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Floating animation */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Skill tag hover effect */
.skill-tag-3d {
  transition: all 0.3s ease;
  transform: translateZ(0);
}

.skill-tag-3d:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 0 15px rgba(103, 232, 249, 0.5);
  z-index: 10;
}

/* Glow effect */
.glow-on-hover {
  position: relative;
  overflow: hidden;
}

.glow-on-hover::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.5s ease;
}

.glow-on-hover:hover::after {
  opacity: 1;
  transform: scale(1);
}
