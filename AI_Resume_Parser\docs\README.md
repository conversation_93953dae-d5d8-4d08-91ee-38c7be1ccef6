# Anil's Portfolio Website

A modern, futuristic portfolio website showcasing my skills and experience as a Software Engineer, AI/ML Engineer, and Data Scientist.

## Features

- **Interactive Terminal Landing Page**: A Mac-inspired terminal interface that welcomes visitors and allows them to navigate to the main portfolio by typing commands
- **Modern UI/UX**: Clean, responsive design with animations and transitions
- **AI-Powered Job Matching**: Integration with Gemini 2.5 Pro for resume and job description parsing to show recruiters how well my profile matches their requirements
- **Dynamic Content**: Animated text showcasing my multiple roles and skills
- **Comprehensive Sections**: Home, About, Education, Work Experience, Projects, Skills, and Contact

## Tech Stack

- **Frontend**: React.js with Next.js
- **Styling**: Tailwind CSS
- **Animation**: Framer Motion
- **AI Integration**: Google Gemini API
- **Deployment**: Vercel

## Getting Started

1. Clone this repository
2. Install dependencies with `npm install`
3. Run the development server with `npm run dev`
4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

The project follows a modular approach with components organized by feature and functionality. See the `plan.md` file for a detailed breakdown of the project structure and implementation plan.

## License

MIT
