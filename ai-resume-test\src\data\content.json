{"personalInfo": {"name": "<PERSON><PERSON>", "displayName": "<PERSON><PERSON>", "titles": ["Software Engineer", "AI/ML Engineer", "Data Scientist"], "email": "<EMAIL>", "phone": "+91 8143400946", "location": "Hyderabad, India", "github": "sahit1011", "linkedin": "anil-sahith", "twitter": "", "website": ""}, "about": {"greeting": "Hello, Myself", "name": "<PERSON><PERSON>", "roles": [{"prefix": "I'm a ", "name": "Software Engineer"}, {"prefix": "I'm a ", "name": "Data Scientist"}, {"prefix": "I'm an ", "name": "AI/ML Engineer"}], "bio": ["With strong Computer Science concepts and an eye for detail, I enjoy creating effective solutions to challenging problems. I'm always excited to use new technologies to build impactful applications.", "My interest in technology started with a fascination for how software can change industries and improve lives. I'm dedicated to learning and finding new ways to solve problems.", "I'm particularly drawn to the dynamic and innovative nature of startup environments. My previous two company experiences were with startups, which I found incredibly rewarding. I'm also actively developing a few startup ideas of my own, including currently building a Doctor assistant app.", "When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or sharing my knowledge through technical writing and mentoring."], "currentlyExploring": ["Agentic AI", "Quantum Computing", "PQC (Post-Quantum Cryptography)"], "education": "B.Tech EEE, NIT Warangal", "experience": "2+ Professional Experiences (Internships & Freelancing)", "technologies": "Proficient in 20+ Technologies"}, "experience": [{"id": 1, "title": "Freelance AI Developer", "company": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "Remote", "period": "Apr 2025 - May 2025", "description": ["Built full-stack Next.js application with TypeScript, MongoDB, and real-time features including interactive drag-and-drop calendar, user authentication, progress analytics, and AI tutor integration serving personalized learning experiences.", "Developed a sophisticated multi-agent AI system using Google Gemini API with specialized agents for monitoring student progress, adapting study plans, and coordinating remediation activities, resulting in 85% improvement over static educational platforms.", "Implemented advanced adaptive learning algorithms combining constraint-based optimization with machine learning for dynamic study plan generation and spaced repetition scheduling based on individual performance patterns.", "Created intelligent monitoring system that tracks user behavior, detects learning difficulties, and automatically adjusts content difficulty and scheduling using sentiment analysis and natural language processing."], "skills": ["Next.js", "TypeScript", "Python", "MongoDB", "Multi-agentic AI", "Google Gemini API", "Machine Learning", "NLP", "Sentiment Analysis", "Progress Analytics", "AI Tutor Integration"]}, {"id": 2, "title": "Junior AI Engineer", "company": "Noccarc Robotics Pvt Ltd.", "location": "Pune, Maharashtra", "period": "Jun 2024 - Apr 2025", "description": ["Developed a real-time arrhythmia detection system for embedded patient monitors using a combined multi-channel hybrid model (CNN-GRU with separable convolutions, an attention layer) to generate predictive masks for 8+ arrhythmia classes, achieving over 95% accuracy.", "System enables real-time detection of heart abnormalities, generating nearly 20+ distinct alarms via the model, facilitating immediate clinical decision-making and reducing response times in critical care settings.", "Architecture includes data flow from ECG sensors, preprocessing, front-end display, back-end processing, and model inference pipelines for seamless embedded hardware operation.", "Model inference occurs in real-time every 5 seconds, generating alarms if any abnormality is detected, all while consuming less than 10% of single-core CPU resources."], "skills": ["Python", "Machine Learning", "Deep Learning", "CNN", "GRU", "Attention Mechanism", "Separable Convolutions", "TF-Lite", "Embedded Systems", "Real-time Processing", "CPU Optimization"]}, {"id": 3, "title": "Summer Intern - AI/ML", "company": "Carelon Global Solutions, (Elevance Healthcare)", "location": "Bangalore, Karnataka", "period": "Jun 2023 - Aug 2023", "description": ["Integrated PyTesseract (OCR), OpenCV (for image preprocessing), and YOLOv8 (for object detection) to extract text and identify regions of interest from EOBs (Explanation of Benefits) and RAs (Remittance Advice) for health insurances, processing ~700 documents/hour with 95% accuracy.", "Implemented multi-threading and multi-processing techniques.", "Delivered a comprehensive, scalable solution for automating data extraction, reducing processing time and cost by 90%."], "skills": ["Python", "PyTesseract", "OpenCV", "YOLOv8", "OCR", "Computer Vision", "Multi-threading", "Multi-processing", "Healthcare IT", "EOB Processing", "RA Processing"]}], "education": [{"id": 1, "degree": "B.Tech in Electrical and Electronics Engineering", "institution": "National Institute of Technology (NIT) Warangal", "location": "Warangal, Telangana", "gpa": "6.5", "achievements": []}], "skills": {"programmingLanguages": [{"name": "Python", "icon": "FaPython"}, {"name": "C++", "icon": "SiCplusplus"}, {"name": "JavaScript", "icon": "FaJs"}, {"name": "TypeScript", "icon": "SiTypescript"}, {"name": "SQL", "icon": "FaDatabase"}, {"name": "Matlab", "icon": "FaLaptopCode"}], "webTechnologies": [{"name": "HTML", "icon": "FaHtml5"}, {"name": "CSS", "icon": "FaCss3Alt"}, {"name": "React", "icon": "FaReact"}, {"name": "Next.js", "icon": "SiNextdotjs"}, {"name": "Node.js", "icon": "FaNodeJs"}, {"name": "Django", "icon": "SiDjango"}, {"name": "Flask", "icon": "SiFlask"}, {"name": "Streamlit", "icon": "FaPython"}, {"name": "Gradio", "icon": "FaPython"}, {"name": "Beautiful Soup", "icon": "FaCodeBranch"}], "databasesAndCloud": [{"name": "MySQL", "icon": "SiMysql"}, {"name": "PostgreSQL", "icon": "SiPostgresql"}, {"name": "SQLite", "icon": "SiSqlite"}, {"name": "MongoDB", "icon": "SiMongodb"}, {"name": "Redis", "icon": "SiRedis"}, {"name": "Pinecone", "icon": "FaDatabase"}, {"name": "Git", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "GCP", "icon": "FaGoogle"}, {"name": "<PERSON>er", "icon": "<PERSON>aDock<PERSON>"}], "aiMl": [{"name": "Deep Learning", "icon": "FaBrain"}, {"name": "Computer Vision", "icon": "FaBrain"}, {"name": "NLP", "icon": "FaBrain"}, {"name": "Agentic AI", "icon": "FaBrain"}, {"name": "Edge ML", "icon": "FaMicrochip"}, {"name": "TensorFlow", "icon": "SiTensorflow"}, {"name": "TF-Lite", "icon": "SiTensorflow"}, {"name": "PyTorch", "icon": "SiPytorch"}, {"name": "Scikit-learn", "icon": "SiScikitlearn"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "icon": "FaBrain"}, {"name": "Hugging Face", "icon": "SiHuggingface"}], "developerTools": [{"name": "VS Code", "icon": "FaCode"}, {"name": "<PERSON>der", "icon": "FaPython"}, {"name": "PyCharm", "icon": "FaPython"}, {"name": "Google Colab", "icon": "FaGoogle"}, {"name": "GitHub", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "icon": "FaLaptopCode"}, {"name": "Power BI", "icon": "FaChartBar"}, {"name": "Figma", "icon": "SiFigma"}, {"name": "Postman", "icon": "FaBrain"}], "conceptsAndSystems": [{"name": "System Design", "icon": "FaServer"}, {"name": "Computer Networks", "icon": "FaNetworkWired"}, {"name": "Embedded Systems", "icon": "FaMicrochip"}, {"name": "IOT and Cloud Computing", "icon": "FaCloud"}, {"name": "Data Structures & Algorithms", "icon": "FaCode"}, {"name": "Quantum Computing", "icon": "FaBrain"}]}, "projects": [{"id": 1, "title": "Custom Stock Trend Predictor", "description": ["Web-based Flask tool for technical analysis with future stock price predictions", "Analyzed multiple forecasting models to achieve most accurate predictions", "Interactive dashboard displaying predicted curves and numerical stock data"], "image": "/placeholder.jpg", "technologies": ["Python", "HTML", "CSS", "JavaScript", "Flask", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Scikit-learn", "<PERSON><PERSON>"], "github": "https://github.com/sahit1011/custom-stock-trend-predictor", "featured": false}, {"id": 2, "title": "Dr. Assistant - AI-Powered Healthcare Solution", "description": ["Comprehensive AI-powered healthcare platform designed to streamline doctor workflows and reduce administrative burden in the Indian healthcare system.", "Features voice-assisted EMR, AI-powered clinical note generation, intelligent scheduling, and seamless integration with India's ABDM health data network.", "Helps doctors focus more on patient care and less on paperwork through advanced automation and AI assistance."], "image": "/placeholder.jpg", "technologies": ["React", "TypeScript", "Material UI", "Node.js", "Express", "PostgreSQL", "MongoDB", "Redis", "Python", "FastAPI", "TensorFlow", "Hugging Face", "<PERSON>er", "Kubernetes", "AWS"], "github": "#", "featured": true}, {"id": 3, "title": "Post-Quantum Crypto Simulator", "description": ["Real-time web app demonstrating RSA vs quantum-resistant algorithms (NTRU, Kyber)", "Interactive chat with live encryption/decryption and attacker simulation mode", "Educational visualizations showcasing post-quantum cryptography concepts"], "image": "/placeholder.jpg", "technologies": ["Python", "Flask", "JavaScript", "Bootstrap", "RSA", "NTRU", "Kyber", "AES-GCM", "WebSocket", "PyCryptodome", "NumPy", "Post-Quantum Cryptography"], "github": "#", "featured": false}, {"id": 4, "title": "Secured Health Prediction using FHE-ML", "description": ["Privacy-preserving health risk prediction system using Fully Homomorphic Encryption (FHE) to enable secure computation on sensitive medical data.", "Advanced ML inference pipeline that performs predictions on encrypted health data without ever exposing sensitive patient information.", "Comprehensive web interface for secure health metrics input and risk assessment."], "image": "/placeholder.jpg", "technologies": ["Python", "Flask", "FHE", "Machine Learning", "Privacy Computing", "Web Security"], "github": "https://github.com/sahit1011/FHE_ML_health_prediction", "featured": true}, {"id": 5, "title": "TodoAI - AI-Powered Task Management Application", "description": ["AI powered Todo app, where you can directly manage your tasks with our AI agent by voice or text, without explicitly performing API calls from frontend.", "Multi-agent AI system for task management through natural language conversations.", "Two AI modes (Plan and Act) with web search integration via LangChain.", "Modern React interface with real-time chat capabilities and FastAPI backend."], "image": "/placeholder.jpg", "technologies": ["React", "FastAPI", "Google Gemini-Pro API", "<PERSON><PERSON><PERSON><PERSON>", "SQLite", "Vite"], "github": "https://github.com/sahit1011/TodoAI", "featured": true}], "resume": {"summary": "Experienced software engineer with expertise in full-stack development, AI/ML, and data science. Passionate about building scalable applications and implementing machine learning solutions to solve complex problems.", "skills": {"languages": "Python, JavaScript, TypeScript, Java, SQL", "frontend": "React, Next.js, HTML/CSS, Tailwind CSS, Redux", "backend": "Node.js, Express, Django, Flask, GraphQL", "databases": "MongoDB, PostgreSQL, MySQL, Redis", "aiMl": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>y<PERSON><PERSON><PERSON>, scikit-learn, NLP, Computer Vision", "devOps": "<PERSON>er, Kubernetes, AWS, GCP, CI/CD"}}, "terminal": {"welcomeMessage": "Welcome to Anil's Terminal Portfolio!", "helpText": "Type help to see available commands.", "themeText": "Use the OS icons in the top-right corner to change the terminal theme.", "prompts": {"windows": "C:\\Users\\<USER>", "mac": "anil@macbook ~ %", "linux": "anil@ubuntu:~$"}, "titles": {"windows": "Command Prompt", "mac": "brew — zsh", "linux": "Terminal - bash"}, "featuredProjects": ["AI-powered recommendation system", "Full-stack e-commerce platform", "Data visualization dashboard"]}, "contact": {"heading": "Get in Touch", "subheading": "Have a question or want to work together? Feel free to contact me!", "availability": ["Full-time positions", "Contract work", "Freelance projects", "Consulting", "Speaking engagements"], "formLabels": {"name": "Name", "email": "Email", "subject": "Subject", "message": "Message", "submit": "Send Message"}}, "meta": {"title": "<PERSON><PERSON> | Software Engineer & AI/ML Engineer", "description": "Personal portfolio of <PERSON><PERSON>, a Software Engineer, AI/ML Engineer, and Data Scientist.", "keywords": ["portfolio", "software engineer", "AI engineer", "ML engineer", "data scientist", "developer"]}, "navigation": {"home": "Home", "about": "About", "experience": "Experience", "projects": "Projects", "skills": "Skills", "aiResumeMatch": "AI Resume Match", "contact": "Contact", "resume": "Resume"}, "footer": {"description": "Software Engineer, AI/ML Engineer, and Data Scientist passionate about building innovative solutions.", "quickLinks": {"title": "Quick Links", "links": [{"name": "Home", "href": "/"}, {"name": "About", "href": "/about"}, {"name": "Experience", "href": "/experience"}, {"name": "Projects", "href": "/projects"}, {"name": "Skills", "href": "/skills"}, {"name": "Contact", "href": "/contact"}, {"name": "Resume", "href": "/resume"}, {"name": "AI Resume Match", "href": "/resume-parser"}]}, "contactSection": {"title": "Contact", "email": "<EMAIL>", "location": "Hyderabad, India", "availability": "Available for freelance and full-time opportunities"}, "socialLinks": {"github": "", "linkedin": "", "twitter": "", "email": ""}, "copyright": "All rights reserved."}, "loading": {"title": "<PERSON><PERSON>'s Portfolio", "message": "Loading terminal environment...", "refreshText": "If the page doesn't load automatically, please click the button below.", "refreshButton": "Refresh Page"}}